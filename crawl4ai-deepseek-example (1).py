import asyncio
import json
import os
from typing import List

from crawl4ai import AsyncWebCrawler, BrowserConfig, CacheMode, CrawlerRunConfig
from crawl4ai.extraction_strategy import LLMExtractionStrategy, LLMConfig
from pydantic import BaseModel, Field

URL_TO_SCRAPE = "https://web.lmarena.ai/leaderboard"

INSTRUCTION_TO_LLM = "Extract all rows from the main table as objects with 'rank', 'model', 'arena score', '95% CI', 'Votes', 'Organization', 'License' from the content."

class TableRow(BaseModel):
    rank: str = Field(..., description="Rank of the model")
    model: str = Field(..., description="Name of the model")
    arena_score: str = Field(..., description="Arena score of the model")
    ci_95: str = Field(..., alias="95% CI", description="95% Confidence Interval")
    votes: str = Field(..., description="Number of votes")
    organization: str = Field(..., description="Organization behind the model")
    license: str = Field(..., description="License type")

async def main():
    # Initialize LLMConfig
    llm_config = LLMConfig(
        provider="deepseek/deepseek-chat",
        api_token=os.getenv("DEEPSEEK_API")
    )

    llm_strategy = LLMExtractionStrategy(
        llm_config=llm_config,
        schema=TableRow.model_json_schema(),
        extraction_type="schema",
        instruction=INSTRUCTION_TO_LLM,
        chunk_token_threshold=1000,
        overlap_rate=0.0,
        apply_chunking=True,
        input_format="markdown",
        extra_args={"temperature": 0.0, "max_tokens": 800},
    )

    crawl_config = CrawlerRunConfig(
        extraction_strategy=llm_strategy,
        cache_mode=CacheMode.BYPASS,
        process_iframes=False,
        remove_overlay_elements=True,
        exclude_external_links=True,
    )

    browser_cfg = BrowserConfig(headless=True, verbose=True)

    async with AsyncWebCrawler(config=browser_cfg) as crawler:
        result = await crawler.arun(url=URL_TO_SCRAPE, config=crawl_config)

        if result.success:
            data = json.loads(result.extracted_content)
            print("Extracted items:", data)
            llm_strategy.show_usage()
        else:
            print("Error:", result.error_message)

if __name__ == "__main__":
    asyncio.run(main())