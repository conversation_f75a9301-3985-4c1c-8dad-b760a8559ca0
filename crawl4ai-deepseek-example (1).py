import asyncio
import json
import os

from crawl4ai import Async<PERSON>ebCrawler, BrowserConfig, CacheMode, CrawlerRunConfig
from crawl4ai.extraction_strategy import LLMExtractionStrategy
from crawl4ai.async_configs import LLMConfig
from pydantic import BaseModel, Field

URL_TO_SCRAPE = "https://web.lmarena.ai/leaderboard"

INSTRUCTION_TO_LLM = "Extract all rows from the main table as objects with 'rank', 'model', 'arena score', '95% CI', 'Votes', 'Organization', 'License' from the content."

class TableRow(BaseModel):
    rank: str = Field(..., description="Rank of the model")
    model: str = Field(..., description="Name of the model")
    arena_score: str = Field(..., description="Arena score of the model")
    ci_95: str = Field(..., alias="95% CI", description="95% Confidence Interval")
    votes: str = Field(..., description="Number of votes")
    organization: str = Field(..., description="Organization behind the model")
    license: str = Field(..., description="License type")

async def main():
    # Check if API token is available
    api_token = os.getenv("DEEPSEEK_API")
    if not api_token:
        print("Warning: DEEPSEEK_API environment variable not set. The extraction may fail.")
        print("Please set your DeepSeek API token: export DEEPSEEK_API=your_token_here")

    # Initialize LLMConfig
    llm_config = LLMConfig(
        provider="deepseek/deepseek-chat",
        api_token=api_token
    )

    llm_strategy = LLMExtractionStrategy(
        llm_config=llm_config,
        schema=TableRow.model_json_schema(),
        extraction_type="schema",
        instruction=INSTRUCTION_TO_LLM,
        chunk_token_threshold=1000,
        overlap_rate=0.0,
        apply_chunking=True,
        input_format="markdown",
        extra_args={"temperature": 0.0, "max_tokens": 800},
    )

    crawl_config = CrawlerRunConfig(
        extraction_strategy=llm_strategy,
        cache_mode=CacheMode.BYPASS,
        process_iframes=False,
        remove_overlay_elements=True,
        exclude_external_links=True,
    )

    browser_cfg = BrowserConfig(headless=True, verbose=True)

    async with AsyncWebCrawler(config=browser_cfg) as crawler:
        result = await crawler.arun(url=URL_TO_SCRAPE, config=crawl_config)

        if result.success:
            print("Crawl successful!")
            print(f"Content length: {len(result.cleaned_html)} characters")
            print(f"Markdown length: {len(result.markdown)} characters")

            if result.extracted_content:
                try:
                    data = json.loads(result.extracted_content)
                    print(f"Extracted items count: {len(data) if isinstance(data, list) else 'N/A'}")
                    print("Extracted items:", data)
                except json.JSONDecodeError as e:
                    print(f"JSON decode error: {e}")
                    print("Raw extracted content:", result.extracted_content[:500] + "..." if len(result.extracted_content) > 500 else result.extracted_content)
            else:
                print("No extracted content available")
                print("First 1000 characters of markdown:")
                print(result.markdown[:1000] + "..." if len(result.markdown) > 1000 else result.markdown)

            llm_strategy.show_usage()
        else:
            print("Error:", result.error_message)

if __name__ == "__main__":
    asyncio.run(main())