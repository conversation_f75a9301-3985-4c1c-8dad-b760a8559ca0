[{"label": "streamlit", "kind": 6, "isExtraImport": true, "importPath": "streamlit", "description": "streamlit", "detail": "streamlit", "documentation": {}}, {"label": "requests", "kind": 6, "isExtraImport": true, "importPath": "requests", "description": "requests", "detail": "requests", "documentation": {}}, {"label": "uuid", "kind": 6, "isExtraImport": true, "importPath": "uuid", "description": "uuid", "detail": "uuid", "documentation": {}}, {"label": "json", "kind": 6, "isExtraImport": true, "importPath": "json", "description": "json", "detail": "json", "documentation": {}}, {"label": "asyncio", "kind": 6, "isExtraImport": true, "importPath": "asyncio", "description": "asyncio", "detail": "asyncio", "documentation": {}}, {"label": "os", "kind": 6, "isExtraImport": true, "importPath": "os", "description": "os", "detail": "os", "documentation": {}}, {"label": "AsyncWebCrawler", "importPath": "crawl4ai", "description": "crawl4ai", "isExtraImport": true, "detail": "crawl4ai", "documentation": {}}, {"label": "BrowserConfig", "importPath": "crawl4ai", "description": "crawl4ai", "isExtraImport": true, "detail": "crawl4ai", "documentation": {}}, {"label": "CacheMode", "importPath": "crawl4ai", "description": "crawl4ai", "isExtraImport": true, "detail": "crawl4ai", "documentation": {}}, {"label": "CrawlerRunConfig", "importPath": "crawl4ai", "description": "crawl4ai", "isExtraImport": true, "detail": "crawl4ai", "documentation": {}}, {"label": "LLMExtractionStrategy", "importPath": "crawl4ai.extraction_strategy", "description": "crawl4ai.extraction_strategy", "isExtraImport": true, "detail": "crawl4ai.extraction_strategy", "documentation": {}}, {"label": "LLMConfig", "importPath": "crawl4ai.async_configs", "description": "crawl4ai.async_configs", "isExtraImport": true, "detail": "crawl4ai.async_configs", "documentation": {}}, {"label": "BaseModel", "importPath": "pydantic", "description": "pydantic", "isExtraImport": true, "detail": "pydantic", "documentation": {}}, {"label": "Field", "importPath": "pydantic", "description": "pydantic", "isExtraImport": true, "detail": "pydantic", "documentation": {}}, {"label": "WEBHOOK_URL", "kind": 5, "importPath": "app", "description": "app", "peekOfCode": "WEBHOOK_URL = \"https://n8n.ibhost.online/webhook/5164df2b-e437-4981-9537-b7f27ef56885\"\nBEARER_TOKEN = \"RGf0W90Jt%8Q\"\n# Initialize session state for chat history and session ID\nif 'chat_history' not in st.session_state:\n    st.session_state.chat_history = []\nif 'session_id' not in st.session_state:\n    st.session_state.session_id = str(uuid.uuid4())\n# Streamlit app layout\nst.title(\"Chat with LLM\")\nst.write(\"Enter your message below to interact with the AI\")", "detail": "app", "documentation": {}}, {"label": "BEARER_TOKEN", "kind": 5, "importPath": "app", "description": "app", "peekOfCode": "BEARER_TOKEN = \"RGf0W90Jt%8Q\"\n# Initialize session state for chat history and session ID\nif 'chat_history' not in st.session_state:\n    st.session_state.chat_history = []\nif 'session_id' not in st.session_state:\n    st.session_state.session_id = str(uuid.uuid4())\n# Streamlit app layout\nst.title(\"Chat with LLM\")\nst.write(\"Enter your message below to interact with the AI\")\n# Display chat history", "detail": "app", "documentation": {}}, {"label": "TableRow", "kind": 6, "importPath": "crawl4ai-deepseek-example (1)", "description": "crawl4ai-deepseek-example (1)", "peekOfCode": "class TableRow(BaseModel):\n    rank: str = Field(..., description=\"Rank of the model\")\n    model: str = Field(..., description=\"Name of the model\")\n    arena_score: str = Field(..., description=\"Arena score of the model\")\n    ci_95: str = Field(..., alias=\"95% CI\", description=\"95% Confidence Interval\")\n    votes: str = Field(..., description=\"Number of votes\")\n    organization: str = Field(..., description=\"Organization behind the model\")\n    license: str = Field(..., description=\"License type\")\nasync def main():\n    # Check if API token is available", "detail": "crawl4ai-deepseek-example (1)", "documentation": {}}, {"label": "URL_TO_SCRAPE", "kind": 5, "importPath": "crawl4ai-deepseek-example (1)", "description": "crawl4ai-deepseek-example (1)", "peekOfCode": "URL_TO_SCRAPE = \"https://web.lmarena.ai/leaderboard\"\nINSTRUCTION_TO_LLM = \"Extract all rows from the main table as objects with 'rank', 'model', 'arena score', '95% CI', 'Votes', 'Organization', 'License' from the content.\"\nclass TableRow(BaseModel):\n    rank: str = Field(..., description=\"Rank of the model\")\n    model: str = Field(..., description=\"Name of the model\")\n    arena_score: str = Field(..., description=\"Arena score of the model\")\n    ci_95: str = Field(..., alias=\"95% CI\", description=\"95% Confidence Interval\")\n    votes: str = Field(..., description=\"Number of votes\")\n    organization: str = Field(..., description=\"Organization behind the model\")\n    license: str = Field(..., description=\"License type\")", "detail": "crawl4ai-deepseek-example (1)", "documentation": {}}, {"label": "INSTRUCTION_TO_LLM", "kind": 5, "importPath": "crawl4ai-deepseek-example (1)", "description": "crawl4ai-deepseek-example (1)", "peekOfCode": "INSTRUCTION_TO_LLM = \"Extract all rows from the main table as objects with 'rank', 'model', 'arena score', '95% CI', 'Votes', 'Organization', 'License' from the content.\"\nclass TableRow(BaseModel):\n    rank: str = Field(..., description=\"Rank of the model\")\n    model: str = Field(..., description=\"Name of the model\")\n    arena_score: str = Field(..., description=\"Arena score of the model\")\n    ci_95: str = Field(..., alias=\"95% CI\", description=\"95% Confidence Interval\")\n    votes: str = Field(..., description=\"Number of votes\")\n    organization: str = Field(..., description=\"Organization behind the model\")\n    license: str = Field(..., description=\"License type\")\nasync def main():", "detail": "crawl4ai-deepseek-example (1)", "documentation": {}}]