import streamlit as st
import requests
import uuid
import json

# Configuration
WEBHOOK_URL = "https://n8n.ibhost.online/webhook/5164df2b-e437-4981-9537-b7f27ef56885"
BEARER_TOKEN = "RGf0W90Jt%8Q"

# Initialize session state for chat history and session ID
if 'chat_history' not in st.session_state:
    st.session_state.chat_history = []
if 'session_id' not in st.session_state:
    st.session_state.session_id = str(uuid.uuid4())

# Streamlit app layout
st.title("Chat with LLM")
st.write("Enter your message below to interact with the AI")

# Display chat history
for message in st.session_state.chat_history:
    with st.chat_message(message["role"]):
        st.markdown(message["content"])

# Chat input
if user_input := st.chat_input("Type your message..."):
    # Add user message to chat history
    st.session_state.chat_history.append({"role": "user", "content": user_input})
    
    # Display user message immediately
    with st.chat_message("user"):
        st.markdown(user_input)
    
    # Prepare payload for webhook
    payload = {
        "sessionId": st.session_state.session_id,
        "chatInput": user_input
    }
    
    # Send request to webhook
    try:
        headers = {
            "Authorization": f"Bearer {BEARER_TOKEN}",
            "Content-Type": "application/json"
        }
        response = requests.post(WEBHOOK_URL, json=payload, headers=headers)
        response.raise_for_status()
        
        # Get LLM response
        llm_response = response.json().get("output", "No response from LLM")
        
        # Add LLM response to chat history
        st.session_state.chat_history.append({"role": "assistant", "content": llm_response})
        
        # Display LLM response
        with st.chat_message("assistant"):
            st.markdown(llm_response)
            
    except requests.RequestException as e:
        error_message = f"Error communicating with LLM: {str(e)}"
        st.session_state.chat_history.append({"role": "assistant", "content": error_message})
        with st.chat_message("assistant"):
            st.markdown(error_message)